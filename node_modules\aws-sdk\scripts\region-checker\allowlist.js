var allowlist = {
    '/config.js': [
        24,
        25,
        85,
        86,
        207,
        261,
        262
    ],
    '/credentials/cognito_identity_credentials.js': [
        87,
        88,
        118
    ],
    '/credentials/shared_ini_file_credentials.js': [
        4,
    ],
    '/credentials/sso_credentials.js': [
        15,
    ],
    '/http.js': [
        5
    ],
    '/rds/signer.js': [
        43,
        44,
        97,
        99,
        110,
        112
    ],
    '/region/utils.js': [
        10
    ],
    '/request.js': [
        318,
        319
    ],
    '/services/s3.js': [
        87,
        88,
        261,
        263,
        276,
        282,
        687,
        689,
        808,
        819,
        820,
        821,
        826,
        1266
    ],
    '/token/sso_token_provider.js': [
        60
    ]
};

module.exports = {
    allowlist: allowlist
};
