!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("lodash/assign"),require("lodash/cloneDeep"),require("lodash/compact"),require("lodash/difference"),require("lodash/functions"),require("lodash/identity"),require("lodash/includes"),require("lodash/isArray"),require("lodash/isElement"),require("lodash/isFunction"),require("lodash/isPlainObject"),require("lodash/isString"),require("lodash/merge"),require("lodash/trim")):"function"==typeof define&&define.amd?define(["lodash/assign","lodash/cloneDeep","lodash/compact","lodash/difference","lodash/functions","lodash/identity","lodash/includes","lodash/isArray","lodash/isElement","lodash/isFunction","lodash/isPlainObject","lodash/isString","lodash/merge","lodash/trim"],e):"object"==typeof exports?exports.cloudinary=e(require("lodash/assign"),require("lodash/cloneDeep"),require("lodash/compact"),require("lodash/difference"),require("lodash/functions"),require("lodash/identity"),require("lodash/includes"),require("lodash/isArray"),require("lodash/isElement"),require("lodash/isFunction"),require("lodash/isPlainObject"),require("lodash/isString"),require("lodash/merge"),require("lodash/trim")):t.cloudinary=e(t._.assign,t._.cloneDeep,t._.compact,t._.difference,t._.functions,t._.identity,t._.includes,t._.isArray,t._.isElement,t._.isFunction,t._.isPlainObject,t._.isString,t._.merge,t._.trim)}(this,(function(t,e,n,r,o,i,u,a,c,s,l,f,p,h){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="./src/namespace/cloudinary-core.js")}({"./src/namespace/cloudinary-core.js":function(t,e,n){"use strict";n.r(e),n.d(e,"ClientHintsMetaTag",(function(){return Kr})),n.d(e,"Cloudinary",(function(){return wo})),n.d(e,"Condition",(function(){return ye})),n.d(e,"Configuration",(function(){return De})),n.d(e,"crc32",(function(){return u})),n.d(e,"Expression",(function(){return ae})),n.d(e,"FetchLayer",(function(){return Ye})),n.d(e,"HtmlTag",(function(){return xn})),n.d(e,"ImageTag",(function(){return sr})),n.d(e,"Layer",(function(){return Oe})),n.d(e,"PictureTag",(function(){return Sr})),n.d(e,"SubtitlesLayer",(function(){return Ve})),n.d(e,"TextLayer",(function(){return Te})),n.d(e,"Transformation",(function(){return kn})),n.d(e,"utf8_encode",(function(){return i})),n.d(e,"Util",(function(){return o})),n.d(e,"VideoTag",(function(){return Mr}));var r={};n.r(r),n.d(r,"VERSION",(function(){return K})),n.d(r,"CF_SHARED_CDN",(function(){return Z})),n.d(r,"OLD_AKAMAI_SHARED_CDN",(function(){return G})),n.d(r,"AKAMAI_SHARED_CDN",(function(){return X})),n.d(r,"SHARED_CDN",(function(){return J})),n.d(r,"DEFAULT_TIMEOUT_MS",(function(){return tt})),n.d(r,"DEFAULT_POSTER_OPTIONS",(function(){return et})),n.d(r,"DEFAULT_VIDEO_SOURCE_TYPES",(function(){return nt})),n.d(r,"SEO_TYPES",(function(){return rt})),n.d(r,"DEFAULT_IMAGE_PARAMS",(function(){return ot})),n.d(r,"DEFAULT_VIDEO_PARAMS",(function(){return it})),n.d(r,"DEFAULT_VIDEO_SOURCES",(function(){return ut})),n.d(r,"DEFAULT_EXTERNAL_LIBRARIES",(function(){return at})),n.d(r,"PLACEHOLDER_IMAGE_MODES",(function(){return ct})),n.d(r,"ACCESSIBILITY_MODES",(function(){return st})),n.d(r,"URL_KEYS",(function(){return lt}));var o={};n.r(o),n.d(o,"getSDKAnalyticsSignature",(function(){return y})),n.d(o,"getAnalyticsOptions",(function(){return v})),n.d(o,"assign",(function(){return b.a})),n.d(o,"cloneDeep",(function(){return w.a})),n.d(o,"compact",(function(){return D.a})),n.d(o,"difference",(function(){return A.a})),n.d(o,"functions",(function(){return O.a})),n.d(o,"identity",(function(){return k.a})),n.d(o,"includes",(function(){return j.a})),n.d(o,"isArray",(function(){return P.a})),n.d(o,"isPlainObject",(function(){return x.a})),n.d(o,"isString",(function(){return I.a})),n.d(o,"merge",(function(){return L.a})),n.d(o,"contains",(function(){return j.a})),n.d(o,"isIntersectionObserverSupported",(function(){return $})),n.d(o,"isNativeLazyLoadSupported",(function(){return Y})),n.d(o,"detectIntersection",(function(){return Q})),n.d(o,"omit",(function(){return pt})),n.d(o,"allStrings",(function(){return yt})),n.d(o,"without",(function(){return dt})),n.d(o,"isNumberLike",(function(){return vt})),n.d(o,"smartEscape",(function(){return mt})),n.d(o,"defaults",(function(){return bt})),n.d(o,"objectProto",(function(){return gt})),n.d(o,"objToString",(function(){return wt})),n.d(o,"isObject",(function(){return _t})),n.d(o,"funcTag",(function(){return Dt})),n.d(o,"reWords",(function(){return At})),n.d(o,"camelCase",(function(){return Bt})),n.d(o,"snakeCase",(function(){return Ot})),n.d(o,"convertKeys",(function(){return Ct})),n.d(o,"withCamelCaseKeys",(function(){return kt})),n.d(o,"withSnakeCaseKeys",(function(){return St})),n.d(o,"base64Encode",(function(){return jt})),n.d(o,"base64EncodeURL",(function(){return Ft})),n.d(o,"extractUrlParams",(function(){return Pt})),n.d(o,"patchFetchFormat",(function(){return Tt})),n.d(o,"optionConsume",(function(){return xt})),n.d(o,"isEmpty",(function(){return Rt})),n.d(o,"isAndroid",(function(){return zt})),n.d(o,"isEdge",(function(){return Lt})),n.d(o,"isChrome",(function(){return Nt})),n.d(o,"isSafari",(function(){return Vt})),n.d(o,"isElement",(function(){return V.a})),n.d(o,"isFunction",(function(){return M.a})),n.d(o,"trim",(function(){return H.a})),n.d(o,"getData",(function(){return qt})),n.d(o,"setData",(function(){return Mt})),n.d(o,"getAttribute",(function(){return Ut})),n.d(o,"setAttribute",(function(){return Ht})),n.d(o,"removeAttribute",(function(){return Wt})),n.d(o,"setAttributes",(function(){return $t})),n.d(o,"hasClass",(function(){return Yt})),n.d(o,"addClass",(function(){return Qt})),n.d(o,"getStyles",(function(){return Kt})),n.d(o,"cssExpand",(function(){return Zt})),n.d(o,"domStyle",(function(){return Gt})),n.d(o,"curCSS",(function(){return Xt})),n.d(o,"cssValue",(function(){return Jt})),n.d(o,"augmentWidthOrHeight",(function(){return te})),n.d(o,"getWidthOrHeight",(function(){return ne})),n.d(o,"width",(function(){return re}));var i=function(t){var e,n,r,o,i,u,a,c;if(null==t)return"";for(c="",i=void 0,r=void 0,0,i=r=0,a=(u=t+"").length,o=0;o<a;)n=null,(e=u.charCodeAt(o))<128?r++:n=e>127&&e<2048?String.fromCharCode(e>>6|192,63&e|128):String.fromCharCode(e>>12|224,e>>6&63|128,63&e|128),null!==n&&(r>i&&(c+=u.slice(i,r)),c+=n,i=r=o+1),o++;return r>i&&(c+=u.slice(i,a)),c};var u=function(t){var e,n,r,o;for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e=0,0,o=0,e^=-1,n=0,r=(t=i(t)).length;n<r;)o=255&(e^t.charCodeAt(n)),e=e>>>8^"0x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substr(9*o,8),n++;return(e^=-1)<0&&(e+=4294967296),e};function a(t,e,n){return e>>=0,n=String(void 0!==n?n:" "),t.length>e?String(t):((e-=t.length)>n.length&&(n+=function(t,e){var n="";for(;e>0;)n+=t,e--;return n}(n,e/n.length)),n.slice(0,e)+String(t))}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var s,l=0,f={};(s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",function(t){if(Array.isArray(t))return c(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}(s)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).forEach((function(t){var e=l.toString(2);e=a(e,6,"0"),f[e]=t,l++}));var p=f;function h(t){var e="",n=6*t.split(".").length,r=function(t){if(t.split(".").length<2)throw new Error("invalid semVer, must have at least two segments");return t.split(".").reverse().map((function(t){return a(t,2,"0")})).join(".")}(t),o=parseInt(r.split(".").join("")).toString(2);if((o=a(o,n,"0")).length%6!=0)throw"Version must be smaller than 43.21.26)";return o.match(/.{1,6}/g).forEach((function(t){e+=p[t]})),e}function y(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var e=d(t.techVersion),n=h(t.sdkSemver),r=h(e),o=t.feature,i=t.sdkCode,u="A";return"".concat(u).concat(i).concat(n).concat(r).concat(o)}catch(a){return"E"}}function d(t){var e=t.split(".");return"".concat(e[0],".").concat(e[1])}function v(t){var e={sdkSemver:t.sdkSemver,techVersion:t.techVersion,sdkCode:t.sdkCode,feature:"0"};return t.urlAnalytics?(t.accessibility&&(e.feature="D"),"lazy"===t.loading&&(e.feature="C"),t.responsive&&(e.feature="A"),t.placeholder&&(e.feature="B"),e):{}}var m=n("lodash/assign"),b=n.n(m),g=n("lodash/cloneDeep"),w=n.n(g),_=n("lodash/compact"),D=n.n(_),E=n("lodash/difference"),A=n.n(E),B=n("lodash/functions"),O=n.n(B),C=n("lodash/identity"),k=n.n(C),S=n("lodash/includes"),j=n.n(S),F=n("lodash/isArray"),P=n.n(F),T=n("lodash/isPlainObject"),x=n.n(T),R=n("lodash/isString"),I=n.n(R),z=n("lodash/merge"),L=n.n(z),N=n("lodash/isElement"),V=n.n(N),q=n("lodash/isFunction"),M=n.n(q),U=n("lodash/trim"),H=n.n(U);function W(t){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function $(){return"object"===("undefined"==typeof window?"undefined":W(window))&&window.IntersectionObserver}function Y(){return"object"===("undefined"==typeof HTMLImageElement?"undefined":W(HTMLImageElement))&&HTMLImageElement.prototype.loading}function Q(t,e){try{if(Y()||!$())return void e();var n=new IntersectionObserver((function(t){t.forEach((function(t){t.isIntersecting&&(e(),n.unobserve(t.target))}))}),{threshold:[0,.01]});n.observe(t)}catch(r){e()}}var K="2.5.0",Z="d3jpl91pxevbkh.cloudfront.net",G="cloudinary-a.akamaihd.net",X="res.cloudinary.com",J=X,tt=1e4,et={format:"jpg",resource_type:"video"},nt=["webm","mp4","ogv"],rt={"image/upload":"images","image/private":"private_images","image/authenticated":"authenticated_images","raw/upload":"files","video/upload":"videos"},ot={resource_type:"image",transformation:[],type:"upload"},it={fallback_content:"",resource_type:"video",source_transformation:{},source_types:nt,transformation:[],type:"upload"},ut=[{type:"mp4",codecs:"hev1",transformations:{video_codec:"h265"}},{type:"webm",codecs:"vp9",transformations:{video_codec:"vp9"}},{type:"mp4",transformations:{video_codec:"auto"}},{type:"webm",transformations:{video_codec:"auto"}}],at={seeThru:"https://unpkg.com/seethru@4/dist/seeThru.min.js"},ct={blur:[{effect:"blur:2000",quality:1,fetch_format:"auto"}],pixelate:[{effect:"pixelate",quality:1,fetch_format:"auto"}],"predominant-color-pixel":[{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:1,height:1,gravity:"north_east"},{fetch_format:"auto",quality:"auto"}],"predominant-color":[{variables:[["$currWidth","w"],["$currHeight","h"]]},{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:10,height:10,gravity:"north_east"},{width:"$currWidth",height:"$currHeight",crop:"fill"},{fetch_format:"auto",quality:"auto"}],vectorize:[{effect:"vectorize:3:0.1",fetch_format:"svg"}]},st={darkmode:"tint:75:black",brightmode:"tint:50:white",monochrome:"grayscale",colorblind:"assist_colorblind"},lt=["accessibility","api_secret","auth_token","cdn_subdomain","cloud_name","cname","format","placeholder","private_cdn","resource_type","secure","secure_cdn_subdomain","secure_distribution","shorten","sign_url","signature","ssl_detected","type","url_suffix","use_root_path","version"];function ft(t){return(ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pt(t,e){t=t||{};var n=Object.keys(t).filter((function(t){return!j()(e,t)})),r={};return n.forEach((function(e){return r[e]=t[e]})),r}var ht,yt=function(t){return t.length&&t.every(I.a)},dt=function(t,e){return t.filter((function(t){return t!==e}))},vt=function(t){return null!=t&&!isNaN(parseFloat(t))},mt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:/([^a-zA-Z0-9_.\-\/:]+)/g;return t.replace(e,(function(t){return t.split("").map((function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})).join("")}))},bt=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return n.reduce((function(t,e){var n,r;for(n in e)r=e[n],void 0===t[n]&&(t[n]=r);return t}),t)},gt=Object.prototype,wt=gt.toString,_t=function(t){var e;return e=ft(t),!!t&&("object"===e||"function"===e)},Dt="[object Function]",Et=function(t){return _t(t)&&wt.call(t)===Dt},At=RegExp("[A-Z]+(?=[A-Z][a-z]+)|[A-Z]?[a-z]+|[A-Z]+|[0-9]+","g"),Bt=function(t){var e=t.match(At);return(e=e.map((function(t){return t.charAt(0).toLocaleUpperCase()+t.slice(1).toLocaleLowerCase()})))[0]=e[0].toLocaleLowerCase(),e.join("")},Ot=function(t){var e=t.match(At);return(e=e.map((function(t){return t.toLocaleLowerCase()}))).join("_")},Ct=function(t,e){var n,r;for(var o in n={},t)r=t[o],e&&(o=e(o)),Rt(o)||(n[o]=r);return n},kt=function(t){return Ct(t,Bt)},St=function(t){return Ct(t,Ot)},jt="undefined"!=typeof btoa&&Et(btoa)?btoa:"undefined"!=typeof Buffer&&Et(Buffer)?function(t){return t instanceof Buffer||(t=new Buffer.from(String(t),"binary")),t.toString("base64")}:function(t){throw new Error("No base64 encoding function found")},Ft=function(t){try{t=decodeURI(t)}finally{t=encodeURI(t)}return jt(t)};function Pt(t){return lt.reduce((function(e,n){return null!=t[n]&&(e[n]=t[n]),e}),{})}function Tt(t){null==t&&(t={}),"fetch"===t.type&&null==t.fetch_format&&(t.fetch_format=xt(t,"format"))}function xt(t,e,n){var r=t[e];return delete t[e],null!=r?r:n}function Rt(t){if(null==t)return!0;if("number"==typeof t.length)return 0===t.length;if("number"==typeof t.size)return 0===t.size;if("object"==ft(t)){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}return!0}function It(){return navigator&&navigator.userAgent||""}function zt(){var t=It();return/Android/i.test(t)}function Lt(){var t=It();return/Edg/i.test(t)}function Nt(){var t=It();return!Lt()&&(/Chrome/i.test(t)||/CriOS/i.test(t))}function Vt(){var t=It();return/Safari/i.test(t)&&!Nt()&&!zt()&&!Lt()}var qt=function(t,e){switch(!1){case!(null==t):return;case!M()(t.getAttribute):return t.getAttribute("data-".concat(e));case!M()(t.getAttr):return t.getAttr("data-".concat(e));case!M()(t.data):return t.data(e);case!(M()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&V()(t)):return jQuery(t).data(e)}},Mt=function(t,e,n){switch(!1){case!(null==t):return;case!M()(t.setAttribute):return t.setAttribute("data-".concat(e),n);case!M()(t.setAttr):return t.setAttr("data-".concat(e),n);case!M()(t.data):return t.data(e,n);case!(M()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&V()(t)):return jQuery(t).data(e,n)}},Ut=function(t,e){switch(!1){case!(null==t):return;case!M()(t.getAttribute):return t.getAttribute(e);case!M()(t.attr):return t.attr(e);case!M()(t.getAttr):return t.getAttr(e)}},Ht=function(t,e,n){switch(!1){case!(null==t):return;case!M()(t.setAttribute):return t.setAttribute(e,n);case!M()(t.attr):return t.attr(e,n);case!M()(t.setAttr):return t.setAttr(e,n)}},Wt=function(t,e){switch(!1){case!(null==t):return;case!M()(t.removeAttribute):return t.removeAttribute(e);default:return Ht(t,void 0)}},$t=function(t,e){var n,r,o;for(n in r=[],e)null!=(o=e[n])?r.push(Ht(t,n,o)):r.push(Wt(t,n));return r},Yt=function(t,e){if(V()(t))return t.className.match(new RegExp("\\b".concat(e,"\\b")))},Qt=function(t,e){if(!t.className.match(new RegExp("\\b".concat(e,"\\b"))))return t.className=H()("".concat(t.className," ").concat(e))},Kt=function(t){return t.ownerDocument.defaultView.opener?t.ownerDocument.defaultView.getComputedStyle(t,null):window.getComputedStyle(t,null)},Zt=["Top","Right","Bottom","Left"];ht=function(t,e){var n,r;return n=9===t.nodeType?t.documentElement:t,t===(r=e&&e.parentNode)||!(!r||1!==r.nodeType||!n.contains(r))};var Gt=function(t,e){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style)return t.style[e]},Xt=function(t,e,n){var r,o,i,u,a,c;return u=/^margin/,c=void 0,o=void 0,r=void 0,i=void 0,a=t.style,(n=n||Kt(t))&&(i=n.getPropertyValue(e)||n[e]),n&&(""!==i||ht(t.ownerDocument,t)||(i=Gt(t,e)),ee.test(i)&&u.test(e)&&(c=a.width,o=a.minWidth,r=a.maxWidth,a.minWidth=a.maxWidth=a.width=i,i=n.width,a.width=c,a.minWidth=o,a.maxWidth=r)),void 0!==i?i+"":i},Jt=function(t,e,n,r){var o;return o=Xt(t,e,r),n?parseFloat(o):o},te=function(t,e,n,r,o){var i,u,a,c,s;if(n===(r?"border":"content"))return 0;for(s=0,i=0,u=(c="width"===e?["Right","Left"]:["Top","Bottom"]).length;i<u;i++)a=c[i],"margin"===n&&(s+=Jt(t,n+a,!0,o)),r?("content"===n&&(s-=Jt(t,"padding".concat(a),!0,o)),"margin"!==n&&(s-=Jt(t,"border".concat(a,"Width"),!0,o))):(s+=Jt(t,"padding".concat(a),!0,o),"padding"!==n&&(s+=Jt(t,"border".concat(a,"Width"),!0,o)));return s},ee=new RegExp("^("+/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source+")(?!px)[a-z%]+$","i"),ne=function(t,e,n){var r,o,i,u;if(u=!0,i="width"===e?t.offsetWidth:t.offsetHeight,o=Kt(t),r="border-box"===Jt(t,"boxSizing",!1,o),i<=0||null==i){if(((i=Xt(t,e,o))<0||null==i)&&(i=t.style[e]),ee.test(i))return i;u=r&&i===t.style[e],i=parseFloat(i)||0}return i+te(t,e,n||(r?"border":"content"),u,o)},re=function(t){return ne(t,"width","content")};function oe(t){return(oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ie(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ue(r.key),r)}}function ue(t){var e=function(t,e){if("object"!=oe(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=oe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==oe(e)?e:e+""}var Expression=function(){function Expression(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,Expression),this.expressions=[],null!=t&&this.expressions.push(Expression.normalize(t))}return t=Expression,n=[{key:"new",value:function(t){return new this(t)}},{key:"normalize",value:function(t){if(null==t)return t;t=String(t);var e=new RegExp("((\\|\\||>=|<=|&&|!=|>|=|<|/|-|\\+|\\*|\\^)(?=[ _]))","g");t=t.replace(e,(function(t){return Expression.OPERATORS[t]}));var n="("+Object.keys(Expression.PREDEFINED_VARS).map((function(t){return":".concat(t,"|").concat(t)})).join("|")+")",r=new RegExp("".concat("(\\$_*[^_ ]+)","|").concat(n),"g");return(t=t.replace(r,(function(t){return Expression.PREDEFINED_VARS[t]||t}))).replace(/[ _]+/g,"_")}},{key:"variable",value:function(t,e){return new this(t).value(e)}},{key:"width",value:function(){return new this("width")}},{key:"height",value:function(){return new this("height")}},{key:"initialWidth",value:function(){return new this("initialWidth")}},{key:"initialHeight",value:function(){return new this("initialHeight")}},{key:"aspectRatio",value:function(){return new this("aspectRatio")}},{key:"initialAspectRatio",value:function(){return new this("initialAspectRatio")}},{key:"pageCount",value:function(){return new this("pageCount")}},{key:"faceCount",value:function(){return new this("faceCount")}},{key:"currentPage",value:function(){return new this("currentPage")}},{key:"tags",value:function(){return new this("tags")}},{key:"pageX",value:function(){return new this("pageX")}},{key:"pageY",value:function(){return new this("pageY")}}],(e=[{key:"serialize",value:function(){return Expression.normalize(this.expressions.join("_"))}},{key:"toString",value:function(){return this.serialize()}},{key:"getParent",value:function(){return this.parent}},{key:"setParent",value:function(t){return this.parent=t,this}},{key:"predicate",value:function(t,e,n){return null!=Expression.OPERATORS[e]&&(e=Expression.OPERATORS[e]),this.expressions.push("".concat(t,"_").concat(e,"_").concat(n)),this}},{key:"and",value:function(){return this.expressions.push("and"),this}},{key:"or",value:function(){return this.expressions.push("or"),this}},{key:"then",value:function(){return this.getParent().if(this.toString())}},{key:"height",value:function(t,e){return this.predicate("h",t,e)}},{key:"width",value:function(t,e){return this.predicate("w",t,e)}},{key:"aspectRatio",value:function(t,e){return this.predicate("ar",t,e)}},{key:"pageCount",value:function(t,e){return this.predicate("pc",t,e)}},{key:"faceCount",value:function(t,e){return this.predicate("fc",t,e)}},{key:"value",value:function(t){return this.expressions.push(t),this}}])&&ie(t.prototype,e),n&&ie(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}();Expression.OPERATORS={"=":"eq","!=":"ne","<":"lt",">":"gt","<=":"lte",">=":"gte","&&":"and","||":"or","*":"mul","/":"div","+":"add","-":"sub","^":"pow"},Expression.PREDEFINED_VARS={aspect_ratio:"ar",aspectRatio:"ar",current_page:"cp",currentPage:"cp",duration:"du",face_count:"fc",faceCount:"fc",height:"h",initial_aspect_ratio:"iar",initial_duration:"idu",initial_height:"ih",initial_width:"iw",initialAspectRatio:"iar",initialDuration:"idu",initialHeight:"ih",initialWidth:"iw",page_count:"pc",page_x:"px",page_y:"py",pageCount:"pc",pageX:"px",pageY:"py",tags:"tags",width:"w"},Expression.BOUNDRY="[ _]+";var ae=Expression;function ce(t){return(ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function se(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,le(r.key),r)}}function le(t){var e=function(t,e){if("object"!=ce(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=ce(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ce(e)?e:e+""}function fe(t,e,n){return e=pe(e),function(t,e){if(e&&("object"===ce(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],pe(t).constructor):e.apply(t,n))}function pe(t){return(pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function he(t,e){return(he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var ye=function(t){function Condition(t){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,Condition),fe(this,Condition,[t])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&he(t,e)}(Condition,t),e=Condition,(n=[{key:"height",value:function(t,e){return this.predicate("h",t,e)}},{key:"width",value:function(t,e){return this.predicate("w",t,e)}},{key:"aspectRatio",value:function(t,e){return this.predicate("ar",t,e)}},{key:"pageCount",value:function(t,e){return this.predicate("pc",t,e)}},{key:"faceCount",value:function(t,e){return this.predicate("fc",t,e)}},{key:"duration",value:function(t,e){return this.predicate("du",t,e)}},{key:"initialDuration",value:function(t,e){return this.predicate("idu",t,e)}}])&&se(e.prototype,n),r&&se(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(ae);function de(t){return(de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ve(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return me(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return me(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function me(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function be(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ge(r.key),r)}}function ge(t){var e=function(t,e){if("object"!=de(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=de(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==de(e)?e:e+""}var we=function(){return t=function Configuration(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,Configuration),this.configuration=null==t?{}:w()(t),bt(this.configuration,_e)},(e=[{key:"init",value:function(){return this.fromEnvironment(),this.fromDocument(),this}},{key:"set",value:function(t,e){return this.configuration[t]=e,this}},{key:"get",value:function(t){return this.configuration[t]}},{key:"merge",value:function(t){return b()(this.configuration,w()(t)),this}},{key:"fromDocument",value:function(){var t,e,n,r;if(r="undefined"!=typeof document&&null!==document?document.querySelectorAll('meta[name^="cloudinary_"]'):void 0)for(e=0,n=r.length;e<n;e++)t=r[e],this.configuration[t.getAttribute("name").replace("cloudinary_","")]=t.getAttribute("content");return this}},{key:"fromEnvironment",value:function(){var t,e,n,r=this;return"undefined"!=typeof process&&null!==process&&process.env&&process.env.CLOUDINARY_URL&&(t=process.env.CLOUDINARY_URL,(n=/cloudinary:\/\/(?:(\w+)(?:\:([\w-]+))?@)?([\w\.-]+)(?:\/([^?]*))?(?:\?(.+))?/.exec(t))&&(null!=n[3]&&(this.configuration.cloud_name=n[3]),null!=n[1]&&(this.configuration.api_key=n[1]),null!=n[2]&&(this.configuration.api_secret=n[2]),null!=n[4]&&(this.configuration.private_cdn=null!=n[4]),null!=n[4]&&(this.configuration.secure_distribution=n[4]),null!=(e=n[5])&&e.split("&").forEach((function(t){var e=ve(t.split("="),2),n=e[0],o=e[1];null==o&&(o=!0),r.configuration[n]=o})))),this}},{key:"config",value:function(t,e){switch(!1){case void 0===e:return this.set(t,e),this.configuration;case!I()(t):return this.get(t);case!x()(t):return this.merge(t),this.configuration;default:return this.configuration}}},{key:"toOptions",value:function(){return w()(this.configuration)}}])&&be(t.prototype,e),n&&be(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}(),_e={responsive_class:"cld-responsive",responsive_use_breakpoints:!0,round_dpr:!0,secure:"https:"===("undefined"!=typeof window&&null!==window&&window.location?window.location.protocol:void 0)};we.CONFIG_PARAMS=["api_key","api_secret","callback","cdn_subdomain","cloud_name","cname","private_cdn","protocol","resource_type","responsive","responsive_class","responsive_use_breakpoints","responsive_width","round_dpr","secure","secure_cdn_subdomain","secure_distribution","shorten","type","upload_preset","url_suffix","use_root_path","version","externalLibraries","max_timeout_ms"];var De=we;function Ee(t){return(Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ae(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Be(r.key),r)}}function Be(t){var e=function(t,e){if("object"!=Ee(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Ee(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ee(e)?e:e+""}var Oe=function(){return t=function Layer(t){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,Layer),this.options={},null!=t&&["resourceType","type","publicId","format"].forEach((function(n){var r;return e.options[n]=null!=(r=t[n])?r:t[Ot(n)]}))},(e=[{key:"resourceType",value:function(t){return this.options.resourceType=t,this}},{key:"type",value:function(t){return this.options.type=t,this}},{key:"publicId",value:function(t){return this.options.publicId=t,this}},{key:"getPublicId",value:function(){var t;return null!=(t=this.options.publicId)?t.replace(/\//g,":"):void 0}},{key:"getFullPublicId",value:function(){return null!=this.options.format?this.getPublicId()+"."+this.options.format:this.getPublicId()}},{key:"format",value:function(t){return this.options.format=t,this}},{key:"toString",value:function(){var t;if(t=[],null==this.options.publicId)throw"Must supply publicId";return"image"!==this.options.resourceType&&t.push(this.options.resourceType),"upload"!==this.options.type&&t.push(this.options.type),t.push(this.getFullPublicId()),D()(t).join(":")}},{key:"clone",value:function(){return new this.constructor(this.options)}}])&&Ae(t.prototype,e),n&&Ae(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}();function Ce(t){return(Ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ke(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Se(r.key),r)}}function Se(t){var e=function(t,e){if("object"!=Ce(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Ce(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ce(e)?e:e+""}function je(t,e,n){return e=Fe(e),function(t,e){if(e&&("object"===Ce(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Fe(t).constructor):e.apply(t,n))}function Fe(t){return(Fe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Pe(t,e){return(Pe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Te=function(t){function TextLayer(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,TextLayer),e=je(this,TextLayer,[t]),null!=t&&["resourceType","resourceType","fontFamily","fontSize","fontWeight","fontStyle","textDecoration","textAlign","stroke","letterSpacing","lineSpacing","fontHinting","fontAntialiasing","text","textStyle"].forEach((function(n){var r;return e.options[n]=null!=(r=t[n])?r:t[Ot(n)]})),e.options.resourceType="text",e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pe(t,e)}(TextLayer,t),e=TextLayer,(n=[{key:"resourceType",value:function(t){throw"Cannot modify resourceType for text layers"}},{key:"type",value:function(t){throw"Cannot modify type for text layers"}},{key:"format",value:function(t){throw"Cannot modify format for text layers"}},{key:"fontFamily",value:function(t){return this.options.fontFamily=t,this}},{key:"fontSize",value:function(t){return this.options.fontSize=t,this}},{key:"fontWeight",value:function(t){return this.options.fontWeight=t,this}},{key:"fontStyle",value:function(t){return this.options.fontStyle=t,this}},{key:"textDecoration",value:function(t){return this.options.textDecoration=t,this}},{key:"textAlign",value:function(t){return this.options.textAlign=t,this}},{key:"stroke",value:function(t){return this.options.stroke=t,this}},{key:"letterSpacing",value:function(t){return this.options.letterSpacing=t,this}},{key:"lineSpacing",value:function(t){return this.options.lineSpacing=t,this}},{key:"fontHinting",value:function(t){return this.options.fontHinting=t,this}},{key:"fontAntialiasing",value:function(t){return this.options.fontAntialiasing=t,this}},{key:"text",value:function(t){return this.options.text=t,this}},{key:"textStyle",value:function(t){return this.options.textStyle=t,this}},{key:"toString",value:function(){var t,e,n,r,o,i,u,a,c,s;if(a=this.textStyleIdentifier(),null!=this.options.publicId&&(r=this.getFullPublicId()),null!=this.options.text){if(e=!Rt(r),n=!Rt(a),e&&n||!e&&!n)throw"Must supply either style parameters or a public_id when providing text parameter in a text overlay/underlay, but not both!";for(o=/\$\([a-zA-Z]\w*\)/g,u=0,s=mt(this.options.text,/[,\/]/g),c="";i=o.exec(s);)c+=mt(s.slice(u,i.index)),c+=i[0],u=i.index+i[0].length;c+=mt(s.slice(u))}return t=[this.options.resourceType,a,r,c],D()(t).join(":")}},{key:"textStyleIdentifier",value:function(){if(!Rt(this.options.textStyle))return this.options.textStyle;var t;if(t=[],"normal"!==this.options.fontWeight&&t.push(this.options.fontWeight),"normal"!==this.options.fontStyle&&t.push(this.options.fontStyle),"none"!==this.options.textDecoration&&t.push(this.options.textDecoration),t.push(this.options.textAlign),"none"!==this.options.stroke&&t.push(this.options.stroke),Rt(this.options.letterSpacing)&&!vt(this.options.letterSpacing)||t.push("letter_spacing_"+this.options.letterSpacing),Rt(this.options.lineSpacing)&&!vt(this.options.lineSpacing)||t.push("line_spacing_"+this.options.lineSpacing),Rt(this.options.fontAntialiasing)||t.push("antialias_"+this.options.fontAntialiasing),Rt(this.options.fontHinting)||t.push("hinting_"+this.options.fontHinting),!Rt(D()(t))){if(Rt(this.options.fontFamily))throw"Must supply fontFamily. ".concat(t);if(Rt(this.options.fontSize)&&!vt(this.options.fontSize))throw"Must supply fontSize."}return t.unshift(this.options.fontFamily,this.options.fontSize),t=D()(t).join("_")}}])&&ke(e.prototype,n),r&&ke(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(Oe);function xe(t){return(xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Re(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ie(r.key),r)}}function Ie(t){var e=function(t,e){if("object"!=xe(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=xe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==xe(e)?e:e+""}function ze(t,e,n){return e=Le(e),function(t,e){if(e&&("object"===xe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Le(t).constructor):e.apply(t,n))}function Le(t){return(Le=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ne(t,e){return(Ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Ve=function(t){function SubtitlesLayer(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,SubtitlesLayer),(e=ze(this,SubtitlesLayer,[t])).options.resourceType="subtitles",e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ne(t,e)}(SubtitlesLayer,t),e=SubtitlesLayer,n&&Re(e.prototype,n),r&&Re(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(Te);function qe(t){return(qe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Me(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ue(r.key),r)}}function Ue(t){var e=function(t,e){if("object"!=qe(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=qe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==qe(e)?e:e+""}function He(t,e,n){return e=We(e),function(t,e){if(e&&("object"===qe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],We(t).constructor):e.apply(t,n))}function We(t){return(We=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function $e(t,e){return($e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Ye=function(t){function FetchLayer(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,FetchLayer),e=He(this,FetchLayer,[t]),I()(t)?e.options.url=t:(null!=t?t.url:void 0)&&(e.options.url=t.url),e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$e(t,e)}(FetchLayer,t),e=FetchLayer,(n=[{key:"url",value:function(t){return this.options.url=t,this}},{key:"toString",value:function(){return"fetch:".concat(Ft(this.options.url))}}])&&Me(e.prototype,n),r&&Me(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(Oe);function Qe(t,e,n){return e=Ge(e),function(t,e){if(e&&("object"===tn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Ge(t).constructor):e.apply(t,n))}function Ke(){return(Ke="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=Ze(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}}).apply(this,arguments)}function Ze(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Ge(t)););return t}function Ge(t){return(Ge=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Xe(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Je(t,e)}function Je(t,e){return(Je=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tn(t){return(tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function en(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,on(r.key),r)}}function rn(t,e,n){return e&&nn(t.prototype,e),n&&nn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function on(t){var e=function(t,e){if("object"!=tn(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tn(e)?e:e+""}var un=function(){return rn((function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:k.a;en(this,t),this.name=e,this.shortName=n,this.process=r}),[{key:"set",value:function(t){return this.origValue=t,this}},{key:"serialize",value:function(){var t,e;return t=this.value(),e=P()(t)||x()(t)||I()(t)?!Rt(t):null!=t,null!=this.shortName&&e?"".concat(this.shortName,"_").concat(t):""}},{key:"value",value:function(){return this.process(this.origValue)}}],[{key:"norm_color",value:function(t){return null!=t?t.replace(/^#/,"rgb:"):void 0}},{key:"build_array",value:function(t){return null==t?[]:P()(t)?t:[t]}},{key:"process_video_params",value:function(t){var e;switch(t.constructor){case Object:return e="","codec"in t&&(e=t.codec,"profile"in t&&(e+=":"+t.profile,"level"in t&&(e+=":"+t.level,"b_frames"in t&&!1===t.b_frames&&(e+=":bframes_no")))),e;case String:return t;default:return null}}}])}(),an=function(t){function e(t,n){var r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return en(this,e),(r=Qe(this,e,[t,n,i])).sep=o,r}return Xe(e,t),rn(e,[{key:"serialize",value:function(){if(null!=this.shortName){var t=this.value();if(Rt(t))return"";if(I()(t))return"".concat(this.shortName,"_").concat(t);var e=t.map((function(t){return M()(t.serialize)?t.serialize():t})).join(this.sep);return"".concat(this.shortName,"_").concat(e)}return""}},{key:"value",value:function(){var t=this;return P()(this.origValue)?this.origValue.map((function(e){return t.process(e)})):this.process(this.origValue)}},{key:"set",value:function(t){return null==t||P()(t)?Ke(Ge(e.prototype),"set",this).call(this,t):Ke(Ge(e.prototype),"set",this).call(this,[t])}}])}(un),cn=function(t){function e(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"t",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return en(this,e),(n=Qe(this,e,[t,r,i])).sep=o,n}return Xe(e,t),rn(e,[{key:"serialize",value:function(){var t=this,e="",n=this.value();if(Rt(n))return e;if(yt(n)){var r=n.join(this.sep);Rt(r)||(e="".concat(this.shortName,"_").concat(r))}else e=n.map((function(e){return I()(e)&&!Rt(e)?"".concat(t.shortName,"_").concat(e):M()(e.serialize)?e.serialize():x()(e)&&!Rt(e)?new kn(e).serialize():void 0})).filter((function(t){return t}));return e}},{key:"set",value:function(t){return this.origValue=t,P()(this.origValue)?Ke(Ge(e.prototype),"set",this).call(this,this.origValue):Ke(Ge(e.prototype),"set",this).call(this,[this.origValue])}}])}(un),sn=function(t){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.norm_range_value;return en(this,e),Qe(this,e,[t,n,r])}return Xe(e,t),rn(e,null,[{key:"norm_range_value",value:function(t){var e=String(t).match(new RegExp("^(([0-9]*)\\.([0-9]+)|([0-9]+))([%pP])?$"));if(e){var n=null!=e[5]?"p":"";t=(e[1]||e[4])+n}return ae.normalize(t)}}])}(un),ln=function(t){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:k.a;return en(this,e),Qe(this,e,[t,n,r])}return Xe(e,t),rn(e,[{key:"serialize",value:function(){return this.value()}}])}(un),fn=function(t){function e(){return en(this,e),Qe(this,e,arguments)}return Xe(e,t),rn(e,[{key:"value",value:function(){if(null==this.origValue)return"";var t;if(this.origValue instanceof Oe)t=this.origValue;else if(x()(this.origValue)){var e=kt(this.origValue);t="text"===e.resourceType||null!=e.text?new Te(e):"subtitles"===e.resourceType?new Ve(e):"fetch"===e.resourceType||null!=e.url?new Ye(e):new Oe(e)}else t=I()(this.origValue)?/^fetch:.+/.test(this.origValue)?new Ye(this.origValue.substr(6)):this.origValue:"";return t.toString()}}],[{key:"textStyle",value:function(t){return new Te(t).textStyleIdentifier()}}])}(un);function pn(t,e,n){return e=hn(e),function(t,e){if(e&&("object"===mn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],hn(t).constructor):e.apply(t,n))}function hn(t){return(hn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yn(t,e){return(yn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return vn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return vn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function mn(t){return(mn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function bn(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,_n(r.key),r)}}function wn(t,e,n){return e&&gn(t.prototype,e),n&&gn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _n(t){var e=function(t,e){if("object"!=mn(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=mn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==mn(e)?e:e+""}function Dn(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return n.forEach((function(e){Object.keys(e).forEach((function(n){null!=e[n]&&(t[n]=e[n])}))})),t}var En=function(){function t(e){var n,r;bn(this,t),n=void 0,r={},this.toOptions=function(t){var e={};if(null==t&&(t=!0),Object.keys(r).forEach((function(t){return e[t]=r[t].origValue})),Dn(e,this.otherOptions),t&&!Rt(this.chained)){var n=this.chained.map((function(t){return t.toOptions()}));n.push(e),Dn(e={},this.otherOptions),e.transformation=n}return e},this.setParent=function(t){return n=t,null!=t&&this.fromOptions("function"==typeof t.toOptions?t.toOptions():void 0),this},this.getParent=function(){return n},this.param=function(t,e,n,o,i){return null==i&&(i=M()(o)?o:k.a),r[e]=new un(e,n,i).set(t),this},this.rawParam=function(t,e,n,o,i){return i=Bn(arguments),r[e]=new ln(e,n,i).set(t),this},this.rangeParam=function(t,e,n,o,i){return i=Bn(arguments),r[e]=new sn(e,n,i).set(t),this},this.arrayParam=function(t,e,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:":",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=Bn(arguments),r[e]=new an(e,n,o,i).set(t),this},this.transformationParam=function(t,e,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=Bn(arguments),r[e]=new cn(e,n,o,i).set(t),this},this.layerParam=function(t,e,n){return r[e]=new fn(e,n).set(t),this},this.getValue=function(t){var e=r[t]&&r[t].value();return null!=e?e:this.otherOptions[t]},this.get=function(t){return r[t]},this.remove=function(t){var e;switch(!1){case null==r[t]:return e=r[t],delete r[t],e.origValue;case null==this.otherOptions[t]:return e=this.otherOptions[t],delete this.otherOptions[t],e;default:return null}},this.keys=function(){var t;return function(){var e;for(t in e=[],r)null!=t&&e.push(t.match(An)?t:Ot(t));return e}().sort()},this.toPlainObject=function(){var t,e,n;for(e in t={},r)t[e]=r[e].value(),x()(t[e])&&(t[e]=w()(t[e]));return Rt(this.chained)||((n=this.chained.map((function(t){return t.toPlainObject()}))).push(t),t={transformation:n}),t},this.chain=function(){var t;return 0!==Object.getOwnPropertyNames(r).length&&(t=new this.constructor(this.toOptions(!1)),this.resetTransformations(),this.chained.push(t)),this},this.resetTransformations=function(){return r={},this},this.otherOptions={},this.chained=[],this.fromOptions(e)}return wn(t,[{key:"fromOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e instanceof t)this.fromTransformation(e);else for(var n in(I()(e)||P()(e))&&(e={transformation:e}),(e=w()(e,(function(e){if(e instanceof t||e instanceof Layer)return new e.clone}))).if&&(this.set("if",e.if),delete e.if),e){var r=e[n];null!=r&&(n.match(An)?"$attr"!==n&&this.set("variable",n,r):this.set(n,r))}return this}},{key:"fromTransformation",value:function(e){var n=this;return e instanceof t&&e.keys().forEach((function(t){return n.set(t,e.get(t).origValue)})),this}},{key:"set",value:function(t){var e;e=Bt(t);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return j()(Cn.methods,e)?this[e].apply(this,r):this.otherOptions[t]=r[0],this}},{key:"hasLayer",value:function(){return this.getValue("overlay")||this.getValue("underlay")}},{key:"serialize",value:function(){var t,e,n,r,o,i,u,a,c,s,l,f,p,h,y,d,v;for(s=this.chained.map((function(t){return t.serialize()})),r=this.keys(),h=null!=(o=this.get("transformation"))?o.serialize():void 0,t=null!=(i=this.get("if"))?i.serialize():void 0,d=function(t){var e,n,r,o,i;if(P()(t)){for(o=[],e=0,n=t.length;e<n;e++){var u=dn(t[e],2);r=u[0],i=u[1],o.push("".concat(r,"_").concat(ae.normalize(i)))}return o}return t}(null!=(u=this.get("variables"))?u.value():void 0),r=A()(r,["transformation","if","variables"]),v=[],f=[],e=0,n=r.length;e<n;e++)(l=r[e]).match(An)?v.push(l+"_"+ae.normalize(null!=(a=this.get(l))?a.value():void 0)):f.push(null!=(c=this.get(l))?c.serialize():void 0);switch(!1){case!I()(h):f.push(h);break;case!P()(h):s=s.concat(h)}return f=function(){var t,e,n;for(n=[],t=0,e=f.length;t<e;t++)y=f[t],(P()(y)&&!Rt(y)||!P()(y)&&y)&&n.push(y);return n}(),f=v.sort().concat(d).concat(f.sort()),"if_end"===t?f.push(t):Rt(t)||f.unshift(t),Rt(p=D()(f).join(this.param_separator))||s.push(p),D()(s).join(this.trans_separator)}},{key:"toHtmlAttributes",value:function(){var t,e,n,r,o,i,u,a,c=this;return n={},Object.keys(this.otherOptions).forEach((function(e){i=c.otherOptions[e],a=Ot(e),j()(Cn.PARAM_NAMES,a)||j()(lt,a)||(t=/^html_/.test(e)?e.slice(5):e,n[t]=i)})),this.keys().forEach((function(t){/^html_/.test(t)&&(n[Bt(t.slice(5))]=c.getValue(t))})),this.hasLayer()||this.getValue("angle")||j()(["fit","limit","lfill"],this.getValue("crop"))||(u=null!=(r=this.get("width"))?r.origValue:void 0,e=null!=(o=this.get("height"))?o.origValue:void 0,parseFloat(u)>=1&&null==n.width&&(n.width=u),parseFloat(e)>=1&&null==n.height&&(n.height=e)),n}},{key:"toHtml",value:function(){var t;return null!=(t=this.getParent())&&"function"==typeof t.toHtml?t.toHtml():void 0}},{key:"toString",value:function(){return this.serialize()}},{key:"clone",value:function(){return new this.constructor(this.toOptions(!0))}}],[{key:"listNames",value:function(){return Cn.methods}},{key:"isValidParamName",value:function(t){return Cn.methods.indexOf(Bt(t))>=0}}])}(),An=/^\$[a-zA-Z0-9]+$/;function Bn(t){var e;return e=null!=t?t[t.length-1]:void 0,M()(e)?e:void 0}function On(t){var e=t.function_type,n=t.source;return"remote"===e?[e,btoa(n)].join(":"):"wasm"===e?[e,n].join(":"):void 0}En.prototype.trans_separator="/",En.prototype.param_separator=",";var Cn=function(t){function Transformation(t){return bn(this,Transformation),pn(this,Transformation,[t])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yn(t,e)}(Transformation,t),wn(Transformation,[{key:"angle",value:function(t){return this.arrayParam(t,"angle","a",".",ae.normalize)}},{key:"audioCodec",value:function(t){return this.param(t,"audio_codec","ac")}},{key:"audioFrequency",value:function(t){return this.param(t,"audio_frequency","af")}},{key:"aspectRatio",value:function(t){return this.param(t,"aspect_ratio","ar",ae.normalize)}},{key:"background",value:function(t){return this.param(t,"background","b",un.norm_color)}},{key:"bitRate",value:function(t){return this.param(t,"bit_rate","br")}},{key:"border",value:function(t){return this.param(t,"border","bo",(function(t){return x()(t)?(t=b()({},{color:"black",width:2},t),"".concat(t.width,"px_solid_").concat(un.norm_color(t.color))):t}))}},{key:"color",value:function(t){return this.param(t,"color","co",un.norm_color)}},{key:"colorSpace",value:function(t){return this.param(t,"color_space","cs")}},{key:"crop",value:function(t){return this.param(t,"crop","c")}},{key:"customFunction",value:function(t){return this.param(t,"custom_function","fn",(function(){return On(t)}))}},{key:"customPreFunction",value:function(t){if(!this.get("custom_function"))return this.rawParam(t,"custom_function","",(function(){return(t=On(t))?"fn_pre:".concat(t):t}))}},{key:"defaultImage",value:function(t){return this.param(t,"default_image","d")}},{key:"delay",value:function(t){return this.param(t,"delay","dl")}},{key:"density",value:function(t){return this.param(t,"density","dn")}},{key:"duration",value:function(t){return this.rangeParam(t,"duration","du")}},{key:"dpr",value:function(t){return this.param(t,"dpr","dpr",(function(t){return(null!=(t=t.toString())?t.match(/^\d+$/):void 0)?t+".0":ae.normalize(t)}))}},{key:"effect",value:function(t){return this.arrayParam(t,"effect","e",":",ae.normalize)}},{key:"else",value:function(){return this.if("else")}},{key:"endIf",value:function(){return this.if("end")}},{key:"endOffset",value:function(t){return this.rangeParam(t,"end_offset","eo")}},{key:"fallbackContent",value:function(t){return this.param(t,"fallback_content")}},{key:"fetchFormat",value:function(t){return this.param(t,"fetch_format","f")}},{key:"format",value:function(t){return this.param(t,"format")}},{key:"flags",value:function(t){return this.arrayParam(t,"flags","fl",".")}},{key:"gravity",value:function(t){return this.param(t,"gravity","g")}},{key:"fps",value:function(t){return this.param(t,"fps","fps",(function(t){return I()(t)?t:P()(t)?t.join("-"):t}))}},{key:"height",value:function(t){var e=this;return this.param(t,"height","h",(function(){return e.getValue("crop")||e.getValue("overlay")||e.getValue("underlay")?ae.normalize(t):null}))}},{key:"htmlHeight",value:function(t){return this.param(t,"html_height")}},{key:"htmlWidth",value:function(t){return this.param(t,"html_width")}},{key:"if",value:function(){var t,e,n,r,o,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";switch(i){case"else":return this.chain(),this.param(i,"if","if");case"end":for(this.chain(),t=n=this.chained.length-1;n>=0&&"end"!==(e=this.chained[t].getValue("if"))&&(null==e||(r=Transformation.new().if(e),this.chained[t].remove("if"),o=this.chained[t],this.chained[t]=Transformation.new().transformation([r,o]),"else"===e));t=n+=-1);return this.param(i,"if","if");case"":return ye.new().setParent(this);default:return this.param(i,"if","if",(function(t){return ye.new(t).toString()}))}}},{key:"keyframeInterval",value:function(t){return this.param(t,"keyframe_interval","ki")}},{key:"ocr",value:function(t){return this.param(t,"ocr","ocr")}},{key:"offset",value:function(t){var e,n,r=dn(M()(null!=t?t.split:void 0)?t.split(".."):P()(t)?t:[null,null],2);if(n=r[0],e=r[1],null!=n&&this.startOffset(n),null!=e)return this.endOffset(e)}},{key:"opacity",value:function(t){return this.param(t,"opacity","o",ae.normalize)}},{key:"overlay",value:function(t){return this.layerParam(t,"overlay","l")}},{key:"page",value:function(t){return this.param(t,"page","pg")}},{key:"poster",value:function(t){return this.param(t,"poster")}},{key:"prefix",value:function(t){return this.param(t,"prefix","p")}},{key:"quality",value:function(t){return this.param(t,"quality","q",ae.normalize)}},{key:"radius",value:function(t){return this.arrayParam(t,"radius","r",":",ae.normalize)}},{key:"rawTransformation",value:function(t){return this.rawParam(t,"raw_transformation")}},{key:"size",value:function(t){var e,n;if(M()(null!=t?t.split:void 0)){var r=dn(t.split("x"),2);return n=r[0],e=r[1],this.width(n),this.height(e)}}},{key:"sourceTypes",value:function(t){return this.param(t,"source_types")}},{key:"sourceTransformation",value:function(t){return this.param(t,"source_transformation")}},{key:"startOffset",value:function(t){return this.rangeParam(t,"start_offset","so")}},{key:"streamingProfile",value:function(t){return this.param(t,"streaming_profile","sp")}},{key:"transformation",value:function(t){return this.transformationParam(t,"transformation","t")}},{key:"underlay",value:function(t){return this.layerParam(t,"underlay","u")}},{key:"variable",value:function(t,e){return this.param(e,t,t)}},{key:"variables",value:function(t){return this.arrayParam(t,"variables")}},{key:"videoCodec",value:function(t){return this.param(t,"video_codec","vc",un.process_video_params)}},{key:"videoSampling",value:function(t){return this.param(t,"video_sampling","vs")}},{key:"width",value:function(t){var e=this;return this.param(t,"width","w",(function(){return e.getValue("crop")||e.getValue("overlay")||e.getValue("underlay")?ae.normalize(t):null}))}},{key:"x",value:function(t){return this.param(t,"x","x",ae.normalize)}},{key:"y",value:function(t){return this.param(t,"y","y",ae.normalize)}},{key:"zoom",value:function(t){return this.param(t,"zoom","z",ae.normalize)}}],[{key:"new",value:function(t){return new Transformation(t)}}])}(En);Cn.methods=["angle","audioCodec","audioFrequency","aspectRatio","background","bitRate","border","color","colorSpace","crop","customFunction","customPreFunction","defaultImage","delay","density","duration","dpr","effect","else","endIf","endOffset","fallbackContent","fetchFormat","format","flags","gravity","fps","height","htmlHeight","htmlWidth","if","keyframeInterval","ocr","offset","opacity","overlay","page","poster","prefix","quality","radius","rawTransformation","size","sourceTypes","sourceTransformation","startOffset","streamingProfile","transformation","underlay","variable","variables","videoCodec","videoSampling","width","x","y","zoom"],Cn.PARAM_NAMES=Cn.methods.map(Ot).concat(De.CONFIG_PARAMS);var kn=Cn;function Sn(t){return(Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function jn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Fn(r.key),r)}}function Fn(t){var e=function(t,e){if("object"!=Sn(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Sn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Sn(e)?e:e+""}function Pn(t,e){return e?!0===e?t:"".concat(t,'="').concat(e,'"'):void 0}function Tn(t){return I()(t)?t.replace('"',"&#34;").replace("'","&#39;"):t}var xn=function(){return t=function HtmlTag(t,e,n){var r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,HtmlTag),this.name=t,this.publicId=e,null==n&&(x()(e)?(n=e,this.publicId=void 0):n={}),(r=new kn(n)).setParent(this),this.transformation=function(){return r}},n=[{key:"new",value:function(t,e,n){return new this(t,e,n)}},{key:"isResponsive",value:function(t,e){var n;return n=qt(t,"src-cache")||qt(t,"src"),Yt(t,e)&&/\bw_auto\b/.exec(n)}}],(e=[{key:"htmlAttrs",value:function(t){var e,n;return function(){var r;for(e in r=[],t)(n=Tn(t[e]))&&r.push(Pn(e,n));return r}().sort().join(" ")}},{key:"getOptions",value:function(){return this.transformation().toOptions()}},{key:"getOption",value:function(t){return this.transformation().getValue(t)}},{key:"attributes",value:function(){var t=this.transformation().toHtmlAttributes();return Object.keys(t).forEach((function(e){x()(t[e])&&delete t[e]})),t.attributes&&(L()(t,t.attributes),delete t.attributes),t}},{key:"setAttr",value:function(t,e){return this.transformation().set("html_".concat(t),e),this}},{key:"getAttr",value:function(t){return this.attributes()["html_".concat(t)]||this.attributes()[t]}},{key:"removeAttr",value:function(t){var e;return null!=(e=this.transformation().remove("html_".concat(t)))?e:this.transformation().remove(t)}},{key:"content",value:function(){return""}},{key:"openTag",value:function(){var t="<"+this.name,e=this.htmlAttrs(this.attributes());return e&&e.length>0&&(t+=" "+e),t+">"}},{key:"closeTag",value:function(){return"</".concat(this.name,">")}},{key:"toHtml",value:function(){return this.openTag()+this.content()+this.closeTag()}},{key:"toDOM",value:function(){var t,e,n,r;if(!M()("undefined"!=typeof document&&null!==document?document.createElement:void 0))throw"Can't create DOM if document is not present!";for(e in t=document.createElement(this.name),n=this.attributes())r=n[e],t.setAttribute(e,r);return t}}])&&jn(t.prototype,e),n&&jn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}(),Rn=["placeholder","accessibility"];function In(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function zn(t){return!!t&&!!t.match(/^https?:\//)}function Ln(t,e){if(e.cloud_name&&"/"===e.cloud_name[0])return"/res"+e.cloud_name;var n="http://",r="",o="res",i=".cloudinary.com",a="/"+e.cloud_name;return e.protocol&&(n=e.protocol+"//"),e.private_cdn&&(r=e.cloud_name+"-",a=""),e.cdn_subdomain&&(o="res-"+function(t){return u(t)%5+1}(t)),e.secure?(n="https://",!1===e.secure_cdn_subdomain&&(o="res"),null!=e.secure_distribution&&e.secure_distribution!==G&&e.secure_distribution!==J&&(r="",o="",i=e.secure_distribution)):e.cname&&(n="http://",r="",o=e.cdn_subdomain?"a"+(u(t)%5+1)+".":"",i=e.cname),[n,r,o,i,a].join("")}function Nn(t){return encodeURIComponent(t).replace(/%3A/g,":").replace(/%2F/g,"/")}function Vn(t){var e=t.cloud_name,n=t.url_suffix;return e?n&&n.match(/[\.\/]/)?"url_suffix should not include . or /":void 0:"Unknown cloud_name"}function qn(t,e){var n,r,o=e.type;return zn(t)||"fetch"!==o?t:(n=t,r=document.location.protocol+"//"+document.location.host,"?"===n[0]?r+=document.location.pathname:"/"!==n[0]&&(r+=document.location.pathname.replace(/\/[^\/]*$/,"/")),r+n)}function Mn(t,e){if(zn(t)&&("upload"===e.type||"asset"===e.type))return t;var n=function(t,e){var n=e.force_version||void 0===e.force_version,r=t.indexOf("/")<0||t.match(/^v[0-9]+/)||zn(t)||e.version;return n&&!r&&(e.version=1),e.version?"v".concat(e.version):""}(t,e),r=function(t){var e=t||{},n=e.placeholder,r=e.accessibility,o=In(e,Rn),i=new kn(o);return r&&st[r]&&i.chain().effect(st[r]),n&&("predominant-color"===n&&i.getValue("width")&&i.getValue("height")&&(n+="-pixel"),(ct[n]||ct.blur).forEach((function(t){return i.chain().transformation(t)}))),i.serialize()}(e),o=Ln(t,e),i=function(t){var e=t.signature,n=!e||0===e.indexOf("s--")&&"--"===e.substr(-2);return delete t.signature,n?e:"s--".concat(e,"--")}(e),u=function(t){var e,n=t.resource_type,r=void 0===n?"image":n,o=t.type,i=void 0===o?"upload":o,u=t.url_suffix,a=t.use_root_path,c=t.shorten,s=r;if(x()(s)&&(s=(e=s).resource_type,i=e.type,c=e.shorten),null==i&&(i="upload"),null!=u&&(s=rt["".concat(s,"/").concat(i)],i=null,null==s))throw new Error("URL Suffix only supported for ".concat(Object.keys(rt).join(", ")));if(a){if(("image"!==s||"upload"!==i)&&"images"!==s)throw new Error("Root path only supported for image/upload");s=null,i=null}return c&&"image"===s&&"upload"===i&&(s="iu",i=null),[s,i].join("/")}(e);return t=function(t,e){if(zn(t))t=Nn(t);else{try{t=decodeURIComponent(t)}catch(n){}t=Nn(t),e.url_suffix&&(t=t+"/"+e.url_suffix),e.format&&(e.trust_public_id||(t=t.replace(/\.(jpg|png|gif|webp)$/,"")),t=t+"."+e.format)}return t}(t,e),D()([o,u,i,r,n,t]).join("/").replace(/([^:])\/+/g,"$1/").replace(" ","%20")}function Un(t,e){return t instanceof kn&&(t=t.toOptions()),"fetch"===(t=bt({},t,e,ot)).type&&(t.fetch_format=t.fetch_format||t.format),t}function Hn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t)return t;t=qn(t,e=Un(e,n));var r=Vn(e);if(r)throw r;var o=Mn(t,e);if(e.urlAnalytics){var i=v(e),u=y(i),a="?";o.indexOf("?")>=0&&(a="&"),o="".concat(o).concat(a,"_a=").concat(u)}if(e.auth_token){var c=o.indexOf("?")>=0?"&":"?";o="".concat(o).concat(c,"__cld_token__=").concat(e.auth_token)}return o}function Wn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,u,a=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(s)throw o}}return a}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return $n(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return $n(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $n(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Yn(t){var e=t.breakpoints||[];if(e.length)return e;var n=Wn([t.min_width,t.max_width,t.max_images].map(Number),3),r=n[0],o=n[1],i=n[2];if([r,o,i].some(isNaN))throw"Either (min_width, max_width, max_images) or breakpoints must be provided to the image srcset attribute";if(r>o)throw"min_width must be less than max_width";if(i<=0)throw"max_images must be a positive integer";1===i&&(r=o);for(var u=Math.ceil((o-r)/Math.max(i-1,1)),a=r;a<o;a+=u)e.push(a);return e.push(o),e}var Qn=Rt;function Kn(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=Pt(r);return n=n||r,o.raw_transformation=new kn([L.a({},n),{crop:"scale",width:e}]).toString(),Hn(t,o)}function Zn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Yn(e)}function Gn(t,e,n,r){return Tt(r=w.a(r)),e.map((function(e){return"".concat(Kn(t,e,n,r)," ").concat(e,"w")})).join(", ")}function Xn(t){return null==t?"":t.map((function(t){return"(max-width: ".concat(t,"px) ").concat(t,"px")})).join(", ")}function Jn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o={};if(Qn(n))return o;var i=!e.sizes&&!0===n.sizes,u=!e.srcset;if(u||i){var a=Zn(t,n,r);if(u){var c=n.transformation,s=Gn(t,a,c,r);Qn(s)||(o.srcset=s)}if(i){var l=Xn(a);Qn(l)||(o.sizes=l)}}return o}function tr(t){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function er(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,rr(r.key),r)}}function rr(t){var e=function(t,e){if("object"!=tr(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tr(e)?e:e+""}function or(t,e,n){return e=ar(e),function(t,e){if(e&&("object"===tr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],ar(t).constructor):e.apply(t,n))}function ir(){return(ir="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=ur(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}}).apply(this,arguments)}function ur(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=ar(t)););return t}function ar(t){return(ar=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function cr(t,e){return(cr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var sr=function(t){function ImageTag(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return er(this,ImageTag),or(this,ImageTag,["img",t,e])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&cr(t,e)}(ImageTag,t),e=ImageTag,(n=[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var t,e,n;t=ir(ar(ImageTag.prototype),"attributes",this).call(this)||{},e=this.getOptions();var r=this.getOption("attributes")||{},o=this.getOption("srcset")||r.srcset,i={};return I()(o)?i.srcset=o:i=Jn(this.publicId,r,o,e),Rt(i)||(delete t.width,delete t.height),L()(t,i),null==t[n=e.responsive&&!e.client_hints?"data-src":"src"]&&(t[n]=Hn(this.publicId,this.getOptions())),t}}])&&nr(e.prototype,n),r&&nr(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(xn);function lr(t){return(lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function pr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,hr(r.key),r)}}function hr(t){var e=function(t,e){if("object"!=lr(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=lr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lr(e)?e:e+""}function yr(t,e,n){return e=mr(e),function(t,e){if(e&&("object"===lr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],mr(t).constructor):e.apply(t,n))}function dr(){return(dr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=vr(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}}).apply(this,arguments)}function vr(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=mr(t)););return t}function mr(t){return(mr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function br(t,e){return(br=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var gr=function(t){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return fr(this,e),yr(this,e,["source",t,n])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&br(t,e)}(e,t),n=e,(r=[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var t=this.getOption("srcset"),n=dr(mr(e.prototype),"attributes",this).call(this)||{},r=this.getOptions();return L()(n,Jn(this.publicId,n,t,r)),n.srcset||(n.srcset=Hn(this.publicId,r)),!n.media&&r.media&&(n.media=function(t){var e=[];return null!=t&&(null!=t.min_width&&e.push("(min-width: ".concat(t.min_width,"px)")),null!=t.max_width&&e.push("(max-width: ".concat(t.max_width,"px)"))),e.join(" and ")}(r.media)),n}}])&&pr(n.prototype,r),o&&pr(n,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,o}(xn);function wr(t){return(wr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Dr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Er(r.key),r)}}function Er(t){var e=function(t,e){if("object"!=wr(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=wr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==wr(e)?e:e+""}function Ar(t,e,n){return e=Cr(e),function(t,e){if(e&&("object"===wr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Cr(t).constructor):e.apply(t,n))}function Br(){return(Br="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=Or(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}}).apply(this,arguments)}function Or(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Cr(t)););return t}function Cr(t){return(Cr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function kr(t,e){return(kr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Sr=function(t){function PictureTag(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return _r(this,PictureTag),(e=Ar(this,PictureTag,["picture",t,n])).widthList=r,e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&kr(t,e)}(PictureTag,t),e=PictureTag,(n=[{key:"content",value:function(){var t=this;return this.widthList.map((function(e){var n=e.min_width,r=e.max_width,o=e.transformation,i=t.getOptions(),u=new kn(i);return u.chain().fromOptions("string"==typeof o?{raw_transformation:o}:o),(i=Pt(i)).media={min_width:n,max_width:r},i.transformation=u,new gr(t.publicId,i).toHtml()})).join("")+new sr(this.publicId,this.getOptions()).toHtml()}},{key:"attributes",value:function(){var t=Br(Cr(PictureTag.prototype),"attributes",this).call(this);return delete t.width,delete t.height,t}},{key:"closeTag",value:function(){return"</"+this.name+">"}}])&&Dr(e.prototype,n),r&&Dr(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(xn);function jr(t){return(jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Fr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Pr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Tr(r.key),r)}}function Tr(t){var e=function(t,e){if("object"!=jr(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=jr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==jr(e)?e:e+""}function xr(t,e,n){return e=zr(e),function(t,e){if(e&&("object"===jr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],zr(t).constructor):e.apply(t,n))}function Rr(){return(Rr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=Ir(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}}).apply(this,arguments)}function Ir(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=zr(t)););return t}function zr(t){return(zr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Lr(t,e){return(Lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Nr=["source_types","source_transformation","fallback_content","poster","sources"],Vr=["webm","mp4","ogv"],qr={format:"jpg",resource_type:"video"},Mr=function(t){function VideoTag(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Fr(this,VideoTag),e=bt({},e,it),xr(this,VideoTag,["video",t.replace(/\.(mp4|ogv|webm)$/,""),e])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Lr(t,e)}(VideoTag,t),e=VideoTag,(n=[{key:"setSourceTransformation",value:function(t){return this.transformation().sourceTransformation(t),this}},{key:"setSourceTypes",value:function(t){return this.transformation().sourceTypes(t),this}},{key:"setPoster",value:function(t){return this.transformation().poster(t),this}},{key:"setFallbackContent",value:function(t){return this.transformation().fallbackContent(t),this}},{key:"content",value:function(){var t=this,e=this.transformation().getValue("source_types"),n=this.transformation().getValue("source_transformation"),r=this.transformation().getValue("fallback_content"),o=this.getOption("sources"),i=[];return P()(o)&&!Rt(o)?i=o.map((function(e){var n=Hn(t.publicId,bt({},e.transformations||{},{resource_type:"video",format:e.type}),t.getOptions());return t.createSourceTag(n,e.type,e.codecs)})):(Rt(e)&&(e=Vr),P()(e)&&(i=e.map((function(e){var r=Hn(t.publicId,bt({},n[e]||{},{resource_type:"video",format:e}),t.getOptions());return t.createSourceTag(r,e)})))),i.join("")+r}},{key:"attributes",value:function(){var t=this.getOption("source_types"),e=this.getOption("poster");if(void 0===e&&(e={}),x()(e)){var n=null!=e.public_id?ot:qr;e=Hn(e.public_id||this.publicId,bt({},e,n,this.getOptions()))}var r=Rr(zr(VideoTag.prototype),"attributes",this).call(this)||{};return r=pt(r,Nr),!Rt(this.getOption("sources"))||Rt(t)||P()(t)||(r.src=Hn(this.publicId,this.getOptions(),{resource_type:"video",format:t})),null!=e&&(r.poster=e),r}},{key:"createSourceTag",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=null;if(!Rt(e)){var o="ogv"===e?"ogg":e;if(r="video/"+o,!Rt(n)){var i=P()(n)?n.join(", "):n;r+="; codecs="+i}}return"<source "+this.htmlAttrs({src:t,type:r})+">"}}])&&Pr(e.prototype,n),r&&Pr(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(xn);function Ur(t){return(Ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Hr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Wr(r.key),r)}}function Wr(t){var e=function(t,e){if("object"!=Ur(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Ur(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ur(e)?e:e+""}function $r(t,e,n){return e=Yr(e),function(t,e){if(e&&("object"===Ur(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,n||[],Yr(t).constructor):e.apply(t,n))}function Yr(t){return(Yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Qr(t,e){return(Qr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Kr=function(t){function ClientHintsMetaTag(t){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,ClientHintsMetaTag),$r(this,ClientHintsMetaTag,["meta",void 0,b()({"http-equiv":"Accept-CH",content:"DPR, Viewport-Width, Width"},t)])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Qr(t,e)}(ClientHintsMetaTag,t),e=ClientHintsMetaTag,(n=[{key:"closeTag",value:function(){return""}}])&&Hr(e.prototype,n),r&&Hr(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(xn);function Zr(t){return function(t){if(Array.isArray(t))return Gr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Gr(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Gr(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gr(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Xr=function(t,e,n,r){return new Promise((function(o,i){t.innerHTML=e.videoTag(n,r).toHtml(),t.querySelector(".cld-transparent-video").style.width="100%",o(t)}))};var Jr=function(t,e){t.transformation?t.transformation.push({flags:[e]}):(t.flags||(t.flags=[]),"string"==typeof t.flags&&(t.flags=[t.flags]),t.flags.push(e))};var to=function(t){t.autoplay=!0,t.muted=!0,t.controls=!1,t.max_timeout_ms=t.max_timeout_ms||tt,t.class=t.class||"",t.class+=" cld-transparent-video",t.externalLibraries=t.externalLibraries||{},t.externalLibraries.seeThru||(t.externalLibraries.seeThru=at.seeThru),Jr(t,"alpha")};var eo=function(t,e,n){return new Promise((function(r,o){if(n)r();else{var i=document.createElement("script");i.src=t;var u=setTimeout((function(){o({status:"error",message:"Timeout loading script ".concat(t)})}),e);i.onerror=function(){clearTimeout(u),o({status:"error",message:"Error loading ".concat(t)})},i.onload=function(){clearTimeout(u),r()},document.head.appendChild(i)}}))};function no(t){return new Promise((function(e,n){fetch(t).then((function(t){t.blob().then((function(t){e(t)}))})).catch((function(){n("error")}))}))}function ro(t){return new Promise((function(e,n){var r=new XMLHttpRequest;r.responseType="blob",r.onload=function(t){e(r.response)},r.onerror=function(){n("error")},r.open("GET",t,!0),r.send()}))}var oo=function(t,e){return new Promise((function(n,r){var o=function(t,e){return setTimeout((function(){e({status:"error",message:"Timeout loading Blob URL"})}),t)}(e,r);("undefined"!=typeof fetch&&fetch?no:ro)(t).then((function(t){n({status:"success",payload:{blobURL:URL.createObjectURL(t)}})})).catch((function(){r({status:"error",message:"Error loading Blob URL"})})).finally((function(){clearTimeout(o)}))}))};var io=function(t){var e=t.autoplay,n=t.playsinline,r=t.loop,o=t.muted,i=t.poster,u=t.blobURL,a=t.videoURL,c=document.createElement("video");return c.style.visibility="hidden",c.position="absolute",c.x=0,c.y=0,c.src=u,c.setAttribute("data-video-url",a),e&&c.setAttribute("autoplay",e),n&&c.setAttribute("playsinline",n),r&&c.setAttribute("loop",r),o&&c.setAttribute("muted",o),o&&(c.muted=o),i&&c.setAttribute("poster",i),c.onload=function(){URL.revokeObjectURL(u)},c};var uo=function(t,e,n,r){var o=window,i=o.seeThru,u=o.setTimeout,a=o.clearTimeout;return new Promise((function(o,c){var s=u((function(){c({status:"error",message:"Timeout instantiating seeThru instance"})}),e);if(i)var l=i.create(t).ready((function(){a(s);var t=l.getCanvas();t.style.width="100%",t.className+=" "+n,r&&l.play(),o(l)}));else c({status:"error",message:"Error instantiating seeThru instance"})}))};var ao=function(t,e,n){var r=n.poster,o=n.autoplay,i=n.playsinline,u=n.loop,a=n.muted;return e+=".mp4",new Promise((function(c,s){eo(n.externalLibraries.seeThru,n.max_timeout_ms,window.seeThru).then((function(){oo(e,n.max_timeout_ms).then((function(l){var f=l.payload,p=io({blobURL:f.blobURL,videoURL:e,poster:r,autoplay:o,playsinline:i,loop:u,muted:a});t.appendChild(p),uo(p,n.max_timeout_ms,n.class,n.autoplay).then((function(){c(t)})).catch((function(t){s(t)}))})).catch((function(t){var e=t.status,n=t.message;s({status:e,message:n})}))})).catch((function(t){var e=t.status,n=t.message;s({status:e,message:n})}))}))};var co,so,lo,fo,po,ho,yo=function(){return new Promise((function(t,e){Vt()&&t(!1);var n=document.createElement("video"),r=n.canPlayType&&n.canPlayType('video/webm; codecs="vp9"');t("maybe"===r||"probably"===r)}))};function vo(t){return(vo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function mo(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,bo(r.key),r)}}function bo(t){var e=function(t,e){if("object"!=vo(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=vo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==vo(e)?e:e+""}lo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return e*Math.ceil(t/e)},so=function(t,e){var n;for(n=t.length-2;n>=0&&t[n]>=e;)n--;return t[n+1]},co=function(t,e,n,r){var o,i,u,a;return!(a=null!=(o=null!=(i=null!=(u=r.responsive_use_breakpoints)?u:r.responsive_use_stoppoints)?i:this.config("responsive_use_breakpoints"))?o:this.config("responsive_use_stoppoints"))||"resize"===a&&!r.resizing?e:this.calc_breakpoint(t,e,n)},fo=function(t){var e,n;for(e=0;(t=null!=t?t.parentNode:void 0)instanceof Element&&!e;)n=window.getComputedStyle(t),/^inline/.test(n.display)||(e=re(t));return e},ho=function(t,e){return t.replace(/\bdpr_(1\.0|auto)\b/g,"dpr_"+this.device_pixel_ratio(e))},po=function(t,e){var n;return t>(n=qt(e,"width")||0)&&(n=t,Mt(e,"width",t)),n};var go=function(){return t=function Cloudinary(t){var e;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,Cloudinary),this.devicePixelRatioCache={},this.responsiveConfig={},this.responsiveResizeInitialized=!1,e=new De(t),this.config=function(t,n){return e.config(t,n)},this.fromDocument=function(){return e.fromDocument(),this},this.fromEnvironment=function(){return e.fromEnvironment(),this},this.init=function(){return e.init(),this}},n=[{key:"new",value:function(t){return new this(t)}}],(e=[{key:"url",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Hn(t,e,this.config())}},{key:"video_url",value:function(t,e){return e=b()({resource_type:"video"},e),this.url(t,e)}},{key:"video_thumbnail_url",value:function(t,e){return e=b()({},et,e),this.url(t,e)}},{key:"transformation_string",value:function(t){return new kn(t).serialize()}},{key:"image",value:function(t){var e,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=this.imageTag(t,o),e=null!=(r=null!=o.client_hints?o.client_hints:this.config("client_hints"))&&r,null!=o.src||e||n.setAttr("src",""),n=n.toDOM(),e||(Mt(n,"src-cache",this.url(t,o)),this.cloudinary_update(n,o)),n}},{key:"imageTag",value:function(t,e){var n;return(n=new sr(t,this.config())).transformation().fromOptions(e),n}},{key:"pictureTag",value:function(t,e,n){var r;return(r=new Sr(t,this.config(),n)).transformation().fromOptions(e),r}},{key:"sourceTag",value:function(t,e){var n;return(n=new gr(t,this.config())).transformation().fromOptions(e),n}},{key:"video_thumbnail",value:function(t,e){return this.image(t,L()({},et,e))}},{key:"facebook_profile_image",value:function(t,e){return this.image(t,b()({type:"facebook"},e))}},{key:"twitter_profile_image",value:function(t,e){return this.image(t,b()({type:"twitter"},e))}},{key:"twitter_name_profile_image",value:function(t,e){return this.image(t,b()({type:"twitter_name"},e))}},{key:"gravatar_image",value:function(t,e){return this.image(t,b()({type:"gravatar"},e))}},{key:"fetch_image",value:function(t,e){return this.image(t,b()({type:"fetch"},e))}},{key:"video",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.videoTag(t,e).toHtml()}},{key:"videoTag",value:function(t,e){return e=bt({},e,this.config()),new Mr(t,e)}},{key:"sprite_css",value:function(t,e){return e=b()({type:"sprite"},e),t.match(/.css$/)||(e.format="css"),this.url(t,e)}},{key:"responsive",value:function(t){var e,n,r,o,i,u=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.responsiveConfig=L()(this.responsiveConfig||{},t),o=null!=(e=this.responsiveConfig.responsive_class)?e:this.config("responsive_class"),a&&this.cloudinary_update("img.".concat(o,", img.cld-hidpi"),this.responsiveConfig),(null==(n=null!=(r=this.responsiveConfig.responsive_resize)?r:this.config("responsive_resize"))||n)&&!this.responsiveResizeInitialized){this.responsiveConfig.resizing=this.responsiveResizeInitialized=!0,i=null;var c=function(){var t,e,n,r,a,c;return t=null!=(e=null!=(n=u.responsiveConfig.responsive_debounce)?n:u.config("responsive_debounce"))?e:100,r=function(){i&&(clearTimeout(i),i=null)},a=function(){return u.cloudinary_update("img.".concat(o),u.responsiveConfig)},c=function(){return r(),a()},t?(r(),void(i=setTimeout(c,t))):a()};return window.addEventListener("resize",c),function(){return window.removeEventListener("resize",c)}}}},{key:"calc_breakpoint",value:function(t,e,n){var r=qt(t,"breakpoints")||qt(t,"stoppoints")||this.config("breakpoints")||this.config("stoppoints")||lo;return M()(r)?r(e,n):(I()(r)&&(r=r.split(",").map((function(t){return parseInt(t)})).sort((function(t,e){return t-e}))),so(r,e))}},{key:"calc_stoppoint",value:function(t,e,n){return this.calc_breakpoint(t,e,n)}},{key:"device_pixel_ratio",value:function(t){t=null==t||t;var e=("undefined"!=typeof window&&null!==window?window.devicePixelRatio:void 0)||1;t&&(e=Math.ceil(e)),(e<=0||NaN===e)&&(e=1);var n=e.toString();return n.match(/^\d+$/)&&(n+=".0"),n}},{key:"processImageTags",value:function(t,e){if(Rt(t))return this;e=bt({},e||{},this.config());var n=t.filter((function(t){return/^img$/i.test(t.tagName)})).map((function(t){var n=b()({width:t.getAttribute("width"),height:t.getAttribute("height"),src:t.getAttribute("src")},e),r=n.source||n.src;delete n.source,delete n.src;var o=new kn(n).toHtmlAttributes();return Mt(t,"src-cache",Hn(r,n)),t.setAttribute("width",o.width),t.setAttribute("height",o.height),t}));return this.cloudinary_update(n,e),this}},{key:"cloudinary_update",value:function(t,e){var n,r,o,i,u=this;if(null===t)return this;null==e&&(e={});var a,c=null!=e.responsive?e.responsive:this.config("responsive");t=function(t){return P()(t)?t:"NodeList"===t.constructor.name?Zr(t):I()(t)?Array.prototype.slice.call(document.querySelectorAll(t),0):[t]}(t),a=this.responsiveConfig&&null!=this.responsiveConfig.responsive_class?this.responsiveConfig.responsive_class:null!=e.responsive_class?e.responsive_class:this.config("responsive_class");var s=null!=e.round_dpr?e.round_dpr:this.config("round_dpr");return t.forEach((function(l){if(/img/i.test(l.tagName)){var f=!0;if(c&&Qt(l,a),!Rt(r=qt(l,"src-cache")||qt(l,"src"))){r=ho.call(u,r,s),xn.isResponsive(l,a)&&(0!==(n=fo(l))?(/w_auto:breakpoints/.test(r)?(i=po(n,l))?r=r.replace(/w_auto:breakpoints([_0-9]*)(:[0-9]+)?/,"w_auto:breakpoints$1:".concat(i)):f=!1:(o=/w_auto(:(\d+))?/.exec(r))&&(i=co.call(u,l,n,o[2],e),(i=po(i,l))?r=r.replace(/w_auto[^,\/]*/g,"w_".concat(i)):f=!1),Wt(l,"width"),e.responsive_preserve_height||Wt(l,"height")):f=!1);var p="lazy"===e.loading&&!u.isNativeLazyLoadSupported()&&u.isLazyLoadSupported()&&!t[0].getAttribute("src");(f||p)&&u.setAttributeIfExists(t[0],"width","data-width"),f&&!p&&Ht(l,"src",r)}}})),this}},{key:"setAttributeIfExists",value:function(t,e,n){var r=t.getAttribute(n);null!=r&&Ht(t,e,r)}},{key:"isLazyLoadSupported",value:function(){return window&&"IntersectionObserver"in window}},{key:"isNativeLazyLoadSupported",value:function(){return"loading"in HTMLImageElement.prototype}},{key:"transformation",value:function(t){return kn.new(this.config()).fromOptions(t).setParent(this)}},{key:"injectTransparentVideoElement",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise((function(o,i){t||i({status:"error",message:"Expecting htmlElContainer to be HTMLElement"}),to(r);var u=n.video_url(e,r);yo().then((function(a){var c;a?(c=Xr(t,n,e,r),o(t)):c=ao(t,u,r),c.then((function(){o(t)})).catch((function(t){var e=t.status,n=t.message;i({status:e,message:n})}))})).catch((function(t){var e=t.status,n=t.message;i({status:e,message:n})}))}))}}])&&mo(t.prototype,e),n&&mo(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}();b()(go,r);var wo=go;e.default={ClientHintsMetaTag:Kr,Cloudinary:wo,Condition:ye,Configuration:De,crc32:u,Expression:ae,FetchLayer:Ye,HtmlTag:xn,ImageTag:sr,Layer:Oe,PictureTag:Sr,SubtitlesLayer:Ve,TextLayer:Te,Transformation:kn,utf8_encode:i,Util:o,VideoTag:Mr}},"lodash/assign":function(e,n){e.exports=t},"lodash/cloneDeep":function(t,n){t.exports=e},"lodash/compact":function(t,e){t.exports=n},"lodash/difference":function(t,e){t.exports=r},"lodash/functions":function(t,e){t.exports=o},"lodash/identity":function(t,e){t.exports=i},"lodash/includes":function(t,e){t.exports=u},"lodash/isArray":function(t,e){t.exports=a},"lodash/isElement":function(t,e){t.exports=c},"lodash/isFunction":function(t,e){t.exports=s},"lodash/isPlainObject":function(t,e){t.exports=l},"lodash/isString":function(t,e){t.exports=f},"lodash/merge":function(t,e){t.exports=p},"lodash/trim":function(t,e){t.exports=h}})}));