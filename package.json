{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.5", "bcrypt": "^5.1.1", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-fileupload": "^1.5.0", "express-session": "^1.18.0", "express-validator": "^7.2.1", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^6.12.8", "mongoose-sequence": "^5.3.1", "morgan": "^1.10.0", "multer": "^2.0.1", "rate-limiter-flexible": "^5.0.5"}, "devDependencies": {"nodemon": "^3.1.0"}}