require('../lib/node_loader');
var AWS = require('../lib/core');
var Service = AWS.Service;
var apiLoader = AWS.apiLoader;

apiLoader.services['cognitosync'] = {};
AWS.CognitoSync = Service.defineService('cognitosync', ['2014-06-30']);
Object.defineProperty(apiLoader.services['cognitosync'], '2014-06-30', {
  get: function get() {
    var model = require('../apis/cognito-sync-2014-06-30.min.json');
    model.paginators = require('../apis/cognito-sync-2014-06-30.paginators.json').pagination;
    return model;
  },
  enumerable: true,
  configurable: true
});

module.exports = AWS.CognitoSync;
