/**
 * SendGrid Email Service
 * Dedicated SendGrid email service for ONetwork Forum
 */

const { RateLimiterMemory } = require("rate-limiter-flexible");
require("dotenv").config();

// Rate limiter for email sending (10 emails per hour per IP)
const emailRateLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip || 'global',
  points: 10, // Number of emails
  duration: 3600, // Per hour
});

class EmailService {
  constructor() {
    this.sendGridClient = null;
    this.initializeSendGrid();
  }

  /**
   * Initialize SendGrid provider
   */
  initializeSendGrid() {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is required. Please set SENDGRID_API_KEY in your environment variables.');
    }

    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    this.sendGridClient = sgMail;
    console.log('Email service initialized with SendGrid');
  }



  /**
   * Validate email address format and domain
   */
  async validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Check for disposable email domains (basic list)
    const disposableDomains = [
      '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
      'mailinator.com', 'throwaway.email'
    ];
    
    const domain = email.split('@')[1].toLowerCase();
    if (disposableDomains.includes(domain)) {
      throw new Error('Disposable email addresses are not allowed');
    }

    return true;
  }

  /**
   * Check rate limit for email sending
   */
  async checkRateLimit(req) {
    try {
      await emailRateLimiter.consume(req.ip || 'global');
      return true;
    } catch (rateLimiterRes) {
      const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;
      throw new Error(`Rate limit exceeded. Try again in ${secs} seconds.`);
    }
  }

  /**
   * Send email using SendGrid
   */
  async sendEmail(options, req = null) {
    try {
      // Validate email
      await this.validateEmail(options.to);

      // Check rate limit if request object is provided
      if (req) {
        await this.checkRateLimit(req);
      }

      // Prepare email data
      const emailData = {
        from: options.from || `${process.env.APP_NAME || 'FAXRN'} <${process.env.FROM_EMAIL}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments || []
      };

      return await this.sendWithSendGrid(emailData);
    } catch (error) {
      console.error('Email sending failed:', error.message);
      throw error;
    }
  }

  /**
   * Send email via SendGrid
   */
  async sendWithSendGrid(emailData) {
    if (!this.sendGridClient) {
      throw new Error('SendGrid client not initialized');
    }

    const msg = {
      to: emailData.to,
      from: emailData.from,
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html,
    };

    try {
      console.log('Attempting to send email via SendGrid:', {
        to: msg.to,
        from: msg.from,
        subject: msg.subject
      });

      const result = await this.sendGridClient.send(msg);
      console.log('Email sent successfully via SendGrid');
      return result;
    } catch (error) {
      console.error('SendGrid error details:', {
        code: error.code,
        message: error.message,
        response: error.response?.body
      });
      throw error;
    }
  }

  /**
   * Test SendGrid configuration
   */
  async testConnection() {
    try {
      if (!this.sendGridClient) {
        console.error('SendGrid client not initialized');
        return false;
      }
      // For SendGrid, we assume it's working if the client is initialized
      console.log('SendGrid connection verified successfully');
      return true;
    } catch (error) {
      console.error('SendGrid connection test failed:', error.message);
      return false;
    }
  }
}

// Export singleton instance
module.exports = new EmailService();
