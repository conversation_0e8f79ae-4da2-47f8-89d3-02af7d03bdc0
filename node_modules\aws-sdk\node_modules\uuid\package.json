{"name": "uuid", "version": "8.0.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./dist/bin/uuid"}, "sideEffects": false, "main": "./dist/index.js", "exports": {"require": "./dist/index.js", "import": "./wrapper.mjs"}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "files": ["CHANGELOG.md", "CONTRIBUTING.md", "LICENSE.md", "README.md", "dist", "wrapper.mjs"], "devDependencies": {"@babel/cli": "7.8.4", "@babel/core": "7.9.0", "@babel/preset-env": "7.9.5", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/browserstack-service": "6.0.12", "@wdio/cli": "6.0.15", "@wdio/jasmine-framework": "6.0.15", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "@wdio/static-server-service": "6.0.13", "@wdio/sync": "6.0.15", "babel-eslint": "10.1.0", "bundlewatch": "0.2.6", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "husky": "4.2.5", "jest": "25.3.0", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "prettier": "2.0.4", "rollup": "2.6.1", "rollup-plugin-terser": "5.3.0", "runmd": "1.3.2", "standard-version": "7.1.0"}, "scripts": {"examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "lint": "npm run eslint:check && npm run prettier:check", "eslint:check": "eslint src/ test/ examples/ *.js", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "pretest": "[ -n $CI ] || npm run build", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "test:browser": "wdio run ./wdio.conf.js", "pretest:node": "npm run build", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "docs:diff": "npm run docs && git diff --quiet README.md", "build": "./scripts/build.sh", "prepack": "npm run build", "release": "standard-version --no-verify"}, "repository": {"type": "git", "url": "https://github.com/uuidjs/uuid.git"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,json,md}": ["prettier --write"], "*.{js,jsx}": ["eslint --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}}