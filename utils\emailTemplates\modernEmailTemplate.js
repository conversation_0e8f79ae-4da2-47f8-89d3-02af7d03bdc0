/**
 * Modern Email Template Base
 * Responsive, accessible, and mobile-friendly email template
 */

const getBaseTemplate = (content, options = {}) => {
  const {
    title = 'ONetwork Forum',
    preheader = '',
    primaryColor = '#007bff',
    backgroundColor = '#f8f9fa',
    textColor = '#333333',
    appName = 'ONetwork Forum',
    appUrl = process.env.REACT_APP_URL || 'http://localhost:3001',
    supportEmail = process.env.SUPPORT_EMAIL || '<EMAIL>'
  } = options;

  return `
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <title>${title}</title>
    
    <!-- Preheader text -->
    <div style="display: none; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: transparent;">
        ${preheader}
    </div>
    
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Typography */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: ${textColor};
            background-color: ${backgroundColor};
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .email-header {
            background-color: ${primaryColor};
            padding: 30px 20px;
            text-align: center;
        }
        
        .email-header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            text-decoration: none;
        }
        
        .email-body {
            padding: 40px 30px;
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .button {
            display: inline-block;
            padding: 16px 32px;
            background-color: ${primaryColor};
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        
        .button:hover {
            background-color: #0056b3;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-muted {
            color: #6c757d;
            font-size: 14px;
        }
        
        .mb-3 {
            margin-bottom: 1rem;
        }
        
        .mb-4 {
            margin-bottom: 1.5rem;
        }
        
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        
        .security-notice p {
            margin: 0;
            color: #856404;
            font-size: 14px;
        }
        
        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .email-body {
                padding: 30px 20px !important;
            }
            
            .email-header {
                padding: 20px 15px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
            
            .button {
                display: block !important;
                width: 100% !important;
                max-width: 300px !important;
                margin: 20px auto !important;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a1a1a !important;
            }
            
            .email-body {
                color: #ffffff !important;
            }
            
            .email-footer {
                background-color: #2d2d2d !important;
                border-top-color: #404040 !important;
            }
        }
    </style>
</head>

<body>
    <div role="article" aria-roledescription="email" lang="en" style="background-color: ${backgroundColor}; padding: 20px 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
                <td align="center">
                    <div class="email-container">
                        <!-- Header -->
                        <div class="email-header">
                            <h1>${appName}</h1>
                        </div>
                        
                        <!-- Body -->
                        <div class="email-body">
                            ${content}
                        </div>
                        
                        <!-- Footer -->
                        <div class="email-footer">
                            <p class="text-muted mb-3">
                                This email was sent by ${appName}. If you have any questions, 
                                please contact us at <a href="mailto:${supportEmail}" style="color: ${primaryColor};">${supportEmail}</a>
                            </p>
                            <p class="text-muted">
                                <a href="${appUrl}" style="color: ${primaryColor}; text-decoration: none;">Visit ${appName}</a> | 
                                <a href="${appUrl}/privacy" style="color: ${primaryColor}; text-decoration: none;">Privacy Policy</a> | 
                                <a href="${appUrl}/terms" style="color: ${primaryColor}; text-decoration: none;">Terms of Service</a>
                            </p>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;
};

module.exports = { getBaseTemplate };
